# Withdrawal Admin Notifications

This document describes the admin notification system for withdrawal events in the Laravel Telegram bot application.

## Overview

The system automatically sends Telegram notifications to administrators whenever a new withdrawal request is created. It supports all withdrawal types and includes multi-language localization.

## Features

- ✅ **Automatic Notifications**: Triggered when any `WidthDrawl` model is created
- ✅ **All Withdrawal Types Supported**: STAR_PURCHASE, PREMIUM_PURCHASE, STAR_WIDTHDRAWL, TON_WIDTHDRAWL
- ✅ **Multi-language Support**: English, Russian, Spanish, Persian, Hindi, Italian
- ✅ **Admin Action Buttons**: Approve/Reject inline keyboard buttons
- ✅ **Error Handling**: Comprehensive logging and error handling
- ✅ **Environment Configuration**: Admin IDs configured via environment variables

## Configuration

### Environment Variables

Set the admin Telegram IDs in your `.env` file:

```env
TELEGRAM_ADMIN_IDS=123456789,987654321,555666777
```

Multiple admin IDs should be separated by commas.

## Implementation Details

### Files Created/Modified

1. **AdminNotificationService** (`Modules/TelegramIstar/app/Services/AdminNotificationService.php`)
   - Main service class handling notification logic
   - Builds messages and keyboards
   - Handles admin ID management

2. **WidthDrawlObserver** (`Modules/TelegramIstar/app/Observers/WidthDrawlObserver.php`)
   - Observer that triggers notifications on model creation
   - Includes error handling and logging

3. **WidthDrawl Model** (`Modules/TelegramIstar/app/Models/WidthDrawl.php`)
   - Added `user()` relationship method
   - Added proper table name configuration

4. **Language Files** (`Modules/TelegramIstar/lang/*/notifications.php`)
   - Localization files for 6 languages
   - Withdrawal type translations
   - Button text translations

5. **Service Provider** (`Modules/TelegramIstar/app/Providers/TelegramIstarServiceProvider.php`)
   - Observer registration
   - Translation loading

## Usage

### Automatic Notifications

Notifications are sent automatically when creating withdrawal records:

```php
use Modules\TelegramIstar\Models\WidthDrawl;
use Modules\TelegramIstar\Enums\WidthDrawlType;

// This will automatically trigger admin notifications
$withdrawal = WidthDrawl::create([
    'user_id' => $user->id,
    'status' => 'pending',
    'amount' => 100.0,
    'address' => 'user_wallet_address',
    'type' => WidthDrawlType::STAR_PURCHASE,
    'note' => 'Optional note',
]);
```

### Manual Notifications

You can also send notifications manually:

```php
use Modules\TelegramIstar\Services\AdminNotificationService;

$notificationService = new AdminNotificationService();
$notificationService->sendWithdrawalNotification($withdrawal);
```

## Message Format

The notification message includes:

- **Withdrawal Type**: With appropriate emoji (⭐ 🔰 💫 💎)
- **User Information**: Username/name and Telegram ID
- **Amount**: Formatted withdrawal amount
- **Address**: Withdrawal address
- **Status**: Current withdrawal status
- **Timestamp**: When the withdrawal was created
- **Note**: Optional user note (if provided)
- **Action Buttons**: Approve/Reject inline keyboard

### Example Message

```
🔔 NEW STAR PURCHASE REQUEST

👤 From: @username (ID: 123456789)
⭐ Amount: 100.00000000
📍 Address: user_wallet_address
📊 Status: pending
🕒 Time: 2025-09-16 14:30:00
📝 Note: Optional user note

Please verify this transaction and approve or reject it.

[✅ Approve] [❌ Reject]
```

## Supported Withdrawal Types

| Type | Constant | Emoji | Description |
|------|----------|-------|-------------|
| Star Purchase | `WidthDrawlType::STAR_PURCHASE` | ⭐ | Star purchase requests |
| Premium Purchase | `WidthDrawlType::PREMIUM_PURCHASE` | 🔰 | Premium membership purchases |
| Star Withdrawal | `WidthDrawlType::STAR_WIDTHDRAWL` | 💫 | Star withdrawal requests |
| TON Withdrawal | `WidthDrawlType::TON_WIDTHDRAWL` | 💎 | TON withdrawal requests |

## Localization

The system supports multiple languages through Laravel's localization system:

- **English** (`en`)
- **Russian** (`ru`)
- **Spanish** (`es`)
- **Persian** (`fa`)
- **Hindi** (`hi`)
- **Italian** (`it`)

Language files are located in `Modules/TelegramIstar/lang/{locale}/notifications.php`.

### Adding New Languages

1. Create a new language directory: `Modules/TelegramIstar/lang/{locale}/`
2. Copy the English notifications file and translate the content
3. The system will automatically use the appropriate language based on Laravel's locale settings

## Error Handling

The system includes comprehensive error handling:

- **Missing User**: Logs warning if withdrawal user is not found
- **No Admin IDs**: Logs warning if no admin IDs are configured
- **Telegram API Errors**: Logs individual failures per admin
- **General Exceptions**: Catches and logs all unexpected errors

All errors are logged with relevant context for debugging.

## Testing

Unit tests are provided in `Modules/TelegramIstar/tests/Unit/AdminNotificationServiceTest.php` to verify:

- Admin ID parsing
- Withdrawal type handling
- Message building
- Keyboard generation
- Error handling

## Integration with Existing Code

The implementation follows the existing codebase patterns:

- Uses the same `TelegramBotClient` service for sending messages
- Follows the Python notification pattern from `pland.md`
- Uses Laravel's observer pattern for automatic triggering
- Implements proper error logging with context
- Uses environment variables for configuration

## Troubleshooting

### No Notifications Received

1. Check `TELEGRAM_ADMIN_IDS` environment variable is set
2. Verify admin IDs are correct Telegram user IDs
3. Check application logs for error messages
4. Ensure the observer is properly registered

### Wrong Language

1. Check Laravel's `APP_LOCALE` configuration
2. Verify the language file exists for the locale
3. Clear Laravel's cache: `php artisan cache:clear`

### Observer Not Triggering

1. Ensure the service provider is loaded
2. Check that `registerObservers()` is called in the boot method
3. Verify the model is being created through Eloquent (not raw SQL)
