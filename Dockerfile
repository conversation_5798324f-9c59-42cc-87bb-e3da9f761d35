FROM dunglas/frankenphp

# Install system dependencies
RUN apt-get update && apt-get install -y \
    git \
    zip \
    unzip \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Install PHP extensions
RUN install-php-extensions \
    pcntl \
    redis \
    pdo_mysql \
    mysqli \
    zip
    # Add other PHP extensions here...

# Create user with same UID/GID as host user
ARG USER_ID=1000
ARG GROUP_ID=1000
RUN groupadd -g ${GROUP_ID} appuser && \
    useradd -u ${USER_ID} -g appuser -m -s /bin/bash appuser

# Install Composer
RUN curl -sS https://getcomposer.org/installer | php -- --install-dir=/usr/local/bin --filename=composer

# Set working directory and permissions
WORKDIR /app
RUN chown -R appuser:appuser /app

# Switch to non-root user first
USER appuser

# Copy composer files for dependency installation
COPY --chown=appuser:appuser composer.json composer.lock ./

# Install dependencies without running scripts
RUN composer install --no-dev --no-scripts --no-interaction

# Copy the rest of the application
COPY --chown=appuser:appuser . .

# Generate basic autoloader (without optimization to avoid database calls)
RUN composer dump-autoload --no-scripts

ENTRYPOINT ["php", "artisan", "octane:frankenphp", "--host=0.0.0.0", "--port=8000"]

# CMD ["php", "artisan", "octane:start", "--server=frankenphp", "--host=0.0.0.0", "--port=8000", "--watch"]
