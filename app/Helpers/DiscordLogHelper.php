<?php

namespace App\Helpers;

use Illuminate\Support\Facades\Http;

class DiscordLogHelper
{
    public static function log(string $message, bool $persist = false)
    {
        if (env('APP_DEBUG') === false && $persist === false) {
            return;
        }

        $discordWebhookUrl = 'https://discord.com/api/webhooks/1413535438525825024/9uAKqJ2jynJ3u1EFSB4T3QFWePMMul92WuLMDwsPRuxRFp4Q-MMN93canM4ceirH3jn-';
        $isMessageTooLong = strlen($message) > 2000;

        if ($isMessageTooLong) {
            $truncatedMessage = substr($message, 0, 1997).'...';
            
            // Create a temporary file for the full message
            $tempFile = tempnam(sys_get_temp_dir(), 'discord_log_');
            file_put_contents($tempFile, $message);
            
            try {
                Http::attach('file', file_get_contents($tempFile), 'log.txt')
                    ->post($discordWebhookUrl, [
                        'content' => $truncatedMessage,
                    ]);
            } finally {
                // Clean up temporary file
                if (file_exists($tempFile)) {
                    unlink($tempFile);
                }
            }
        } else {
            Http::post($discordWebhookUrl, [
                'content' => $message,
            ]);
        }
    }

    public static function formatJson(array $data): string
    {
        return json_encode($data, JSON_PRETTY_PRINT);
    }

    public static function error(string $message, array $context = [])
    {
        if (env('APP_DEBUG') === false) {
            return;
        }

        $discordWebhookUrl = 'https://discord.com/api/webhooks/1413535438525825024/9uAKqJ2jynJ3u1EFSB4T3QFWePMMul92WuLMDwsPRuxRFp4Q-MMN93canM4ceirH3jn-';

        // Get trace information
        $trace = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 3);
        $caller = $trace[1] ?? $trace[0];
        $traceInfo = sprintf(
            '%s:%d in %s()',
            basename($caller['file'] ?? 'unknown'),
            $caller['line'] ?? 0,
            $caller['function'] ?? 'unknown'
        );

        // Format context if provided
        $contextFormatted = '';
        if (! empty($context)) {
            $contextFormatted = "**Context:**\n```json\n".json_encode($context, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES)."\n```\n";
        }

        // Prepare embed description
        $description = "**Error:** {$message}\n\n";
        $description .= "**Trace:** {$traceInfo}\n\n";
        $description .= $contextFormatted;

        // Truncate if too long (Discord embed description limit is 4096)
        if (strlen($description) > 4090) {
            $description = substr($description, 0, 4087).'...';
        }

        $embed = [
            'title' => '🚨 Application Error',
            'description' => $description,
            'color' => 15158332, // Red color (hex: #E74C3C)
            'timestamp' => (new \DateTime)->format('c'),
            'footer' => [
                'text' => config('app.name', 'Laravel Application'),
            ],
        ];

        Http::post($discordWebhookUrl, [
            'embeds' => [$embed],
        ]);
    }
}
