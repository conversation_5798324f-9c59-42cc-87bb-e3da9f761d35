<?php

namespace Modules\TelegramClient\Services;

use App\Helpers\DiscordLogHelper;
use App\Models\User;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class TelegramBotClient
{
    protected $baseUrl;

    public function __construct(?string $botToken = null)
    {
        if (! $botToken) {
            $tenant = app('currentTenant');
            $botToken = $tenant->token;
        }

        if (! $botToken) {
            throw new \InvalidArgumentException('Bot token is required');
        }

        $this->baseUrl = 'https://api.telegram.org/bot'.$botToken.'/';
    }

    public function setWebhookUrl($url, $secret): bool
    {
        $response = Http::timeout(30)->get($this->baseUrl.'setWebhook', [
            'url' => $url,
            'secret_token' => $secret,
        ]);
        $result = $response->json();

        return isset($result['ok']) && $result['ok'];
    }

    public function getBotInfo(): array
    {
        try {
            $response = Http::get("{$this->baseUrl}getMe");

            if ($response->successful()) {
                $re = $response->json();

                return [
                    'success' => true,
                    'data' => [
                        'id' => $re['result']['id'],
                        'username' => $re['result']['username'],
                        'first_name' => $re['result']['first_name'],
                        'is_bot' => $re['result']['is_bot'],
                        'can_join_groups' => $re['result']['can_join_groups'],
                    ],
                ];
            }

            // Nếu token không hợp lệ sẽ trả về 401
            if ($response->status() === 401) {
                throw new \Exception('Invalid bot token');
            }

            throw new \Exception('Failed to get bot info: '.$response->body());
        } catch (\Exception $e) {
            throw new \Exception('Error connecting to Telegram API: '.$e->getMessage());
        }
    }

    /**
     * Create a Telegram Stars invoice link
     *
     * @param  User  $user  The user for whom the invoice is being created
     * @param  array  $data  Invoice data containing title, description, payload, currency, prices, etc.
     * @return array Response containing invoice link or error information
     */
    public function createInvoiceLink(array $data): array
    {
        try {
            $requiredFields = ['title', 'description', 'payload', 'prices'];
            foreach ($requiredFields as $field) {
                if (! isset($data[$field]) || empty($data[$field])) {
                    return [
                        'success' => false,
                        'message' => "Missing required field: {$field}",
                    ];
                }
            }

            // Prepare the request payload
            $payload = [
                'title' => $data['title'],
                'description' => $data['description'],
                'payload' => $data['payload'],
                'provider_token' => '',
                'currency' => 'XTR',
                'prices' => $data['prices'],
            ];

            // Add optional fields if provided
            $optionalFields = [
                'max_tip_amount',
                'suggested_tip_amounts',
                'start_parameter',
                'provider_data',
                'photo_url',
                'photo_size',
                'photo_width',
                'photo_height',
                'need_name',
                'need_phone_number',
                'need_email',
                'need_shipping_address',
                'send_phone_number_to_provider',
                'send_email_to_provider',
                'is_flexible',
            ];

            foreach ($optionalFields as $field) {
                if (isset($data[$field])) {
                    $payload[$field] = $data[$field];
                }
            }

            // Make the API request to Telegram
            $response = Http::timeout(30)
                ->withHeaders([
                    'Content-Type' => 'application/json',
                    'Accept' => 'application/json',
                ])
                ->post($this->baseUrl.'createInvoiceLink', $payload);

            // Check if the request was successful
            if (! $response->successful()) {
                Log::error('Telegram createInvoiceLink API error', [
                    'status' => $response->status(),
                    'response' => $response->body(),
                    'payload' => $payload,
                ]);

                return [
                    'success' => false,
                    'message' => 'Failed to create invoice link',
                    'error' => $response->json()['description'] ?? 'Unknown error',
                ];
            }

            $responseData = $response->json();

            // Check if Telegram API returned success
            if (! $responseData['ok']) {
                Log::error('Telegram createInvoiceLink API returned error', [
                    'response' => $responseData,
                    'payload' => $payload,
                ]);

                return [
                    'success' => false,
                    'message' => 'Telegram API error',
                    'error' => $responseData['description'] ?? 'Unknown error',
                ];
            }

            return [
                'success' => true,
                'invoice_link' => $responseData['result'],
            ];

        } catch (\Exception $e) {
            Log::error('Exception in createInvoiceLink', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'data' => $data,
            ]);

            return [
                'success' => false,
                'message' => 'An error occurred while creating the invoice link',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Send a message to a chat
     */
    public function sendMessage(int|string $chatId, string $text, array $options = []): array
    {
        try {
            $params = array_merge([
                'chat_id' => $chatId,
                'text' => htmlspecialchars($text, ENT_QUOTES | ENT_SUBSTITUTE, 'UTF-8'),
                'parse_mode' => 'HTML',
            ], $options);

            $response = Http::timeout(30)
                ->withHeaders([
                    'Content-Type' => 'application/json',
                    'Accept' => 'application/json',
                ])
                ->post($this->baseUrl.'sendMessage', $params);

            if (! $response->successful()) {
                DiscordLogHelper::error('Failed to send Telegram message', [
                    'chat_id' => $chatId,
                    'text' => $text,
                    'status' => $response->status(),
                    'response' => $response->body(),
                ]);

                return [
                    'success' => false,
                    'error' => $response->json()['description'] ?? 'Unknown error',
                ];
            }

            $responseData = $response->json();

            if (! $responseData['ok']) {
                DiscordLogHelper::error('Telegram sendMessage API returned error', [
                    'chat_id' => $chatId,
                    'text' => $text,
                    'response' => $responseData,
                ]);

                return [
                    'success' => false,
                    'error' => $responseData['description'] ?? 'Unknown error',
                ];
            }

            return [
                'success' => true,
                'data' => $responseData['result'],
            ];
        } catch (\Exception $e) {
            DiscordLogHelper::error('Exception in sendMessage', [
                'chat_id' => $chatId,
                'text' => $text,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Send a photo to a chat
     */
    public function sendPhoto(int|string $chatId, string $photo, string $caption = '', array $options = []): array
    {
        try {
            $params = array_merge([
                'chat_id' => $chatId,
                'photo' => $photo,
                'caption' => $caption,
                'parse_mode' => 'HTML',
            ], $options);

            $response = Http::timeout(30)
                ->withHeaders([
                    'Content-Type' => 'application/json',
                    'Accept' => 'application/json',
                ])
                ->post($this->baseUrl.'sendPhoto', $params);

            if (! $response->successful()) {
                Log::error('Failed to send Telegram photo', [
                    'chat_id' => $chatId,
                    'photo' => $photo,
                    'caption' => $caption,
                    'status' => $response->status(),
                    'response' => $response->body(),
                ]);

                return [
                    'success' => false,
                    'error' => $response->json()['description'] ?? 'Unknown error',
                ];
            }

            $responseData = $response->json();

            if (! $responseData['ok']) {
                Log::error('Telegram sendPhoto API returned error', [
                    'chat_id' => $chatId,
                    'photo' => $photo,
                    'caption' => $caption,
                    'response' => $responseData,
                ]);

                return [
                    'success' => false,
                    'error' => $responseData['description'] ?? 'Unknown error',
                ];
            }

            // Log outgoing photo if enabled
            if (config('telegrambot.logging.outgoing_messages', true)) {
                Log::info('Telegram photo sent', [
                    'chat_id' => $chatId,
                    'photo' => $photo,
                    'caption' => $caption,
                    'message_id' => $responseData['result']['message_id'] ?? null,
                ]);
            }

            return [
                'success' => true,
                'data' => $responseData['result'],
            ];
        } catch (\Exception $e) {
            Log::error('Exception in sendPhoto', [
                'chat_id' => $chatId,
                'photo' => $photo,
                'caption' => $caption,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Edit a message
     */
    public function editMessage(int|string $chatId, int $messageId, string $text, array $options = []): array
    {
        try {
            $params = array_merge([
                'chat_id' => $chatId,
                'message_id' => $messageId,
                'text' => $text,
                'parse_mode' => 'HTML',
            ], $options);

            $response = Http::timeout(30)
                ->withHeaders([
                    'Content-Type' => 'application/json',
                    'Accept' => 'application/json',
                ])
                ->post($this->baseUrl.'editMessageText', $params);

            if (! $response->successful()) {
                Log::error('Failed to edit Telegram message', [
                    'chat_id' => $chatId,
                    'message_id' => $messageId,
                    'text' => $text,
                    'status' => $response->status(),
                    'response' => $response->body(),
                ]);

                return [
                    'success' => false,
                    'error' => $response->json()['description'] ?? 'Unknown error',
                ];
            }

            $responseData = $response->json();

            if (! $responseData['ok']) {
                Log::error('Telegram editMessage API returned error', [
                    'chat_id' => $chatId,
                    'message_id' => $messageId,
                    'text' => $text,
                    'response' => $responseData,
                ]);

                return [
                    'success' => false,
                    'error' => $responseData['description'] ?? 'Unknown error',
                ];
            }

            return [
                'success' => true,
                'data' => $responseData['result'],
            ];
        } catch (\Exception $e) {
            Log::error('Exception in editMessage', [
                'chat_id' => $chatId,
                'message_id' => $messageId,
                'text' => $text,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Delete a message
     */
    public function deleteMessage(int|string $chatId, int $messageId): array
    {
        try {
            $params = [
                'chat_id' => $chatId,
                'message_id' => $messageId,
            ];

            $response = Http::timeout(30)
                ->withHeaders([
                    'Content-Type' => 'application/json',
                    'Accept' => 'application/json',
                ])
                ->post($this->baseUrl.'deleteMessage', $params);

            if (! $response->successful()) {
                Log::error('Failed to delete Telegram message', [
                    'chat_id' => $chatId,
                    'message_id' => $messageId,
                    'status' => $response->status(),
                    'response' => $response->body(),
                ]);

                return [
                    'success' => false,
                    'error' => $response->json()['description'] ?? 'Unknown error',
                ];
            }

            $responseData = $response->json();

            if (! $responseData['ok']) {
                Log::error('Telegram deleteMessage API returned error', [
                    'chat_id' => $chatId,
                    'message_id' => $messageId,
                    'response' => $responseData,
                ]);

                return [
                    'success' => false,
                    'error' => $responseData['description'] ?? 'Unknown error',
                ];
            }

            return [
                'success' => true,
                'data' => $responseData['result'],
            ];
        } catch (\Exception $e) {
            Log::error('Exception in deleteMessage', [
                'chat_id' => $chatId,
                'message_id' => $messageId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Answer callback query
     */
    public function answerCallbackQuery(string $callbackQueryId, string $text = '', bool $showAlert = false): array
    {
        try {
            $params = [
                'callback_query_id' => $callbackQueryId,
                'text' => $text,
                'show_alert' => $showAlert,
            ];

            $response = Http::timeout(30)
                ->withHeaders([
                    'Content-Type' => 'application/json',
                    'Accept' => 'application/json',
                ])
                ->post($this->baseUrl.'answerCallbackQuery', $params);

            if (! $response->successful()) {
                Log::error('Failed to answer callback query', [
                    'callback_query_id' => $callbackQueryId,
                    'text' => $text,
                    'status' => $response->status(),
                    'response' => $response->body(),
                ]);

                return [
                    'success' => false,
                    'error' => $response->json()['description'] ?? 'Unknown error',
                ];
            }

            $responseData = $response->json();

            if (! $responseData['ok']) {
                Log::error('Telegram answerCallbackQuery API returned error', [
                    'callback_query_id' => $callbackQueryId,
                    'text' => $text,
                    'response' => $responseData,
                ]);

                return [
                    'success' => false,
                    'error' => $responseData['description'] ?? 'Unknown error',
                ];
            }

            return [
                'success' => true,
                'data' => $responseData['result'],
            ];
        } catch (\Exception $e) {
            Log::error('Exception in answerCallbackQuery', [
                'callback_query_id' => $callbackQueryId,
                'text' => $text,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    public function answerPreCheckoutQuery(string $preCheckoutQueryId, bool $ok): array
    {
        $payload = [
            'pre_checkout_query_id' => $preCheckoutQueryId,
            'ok' => $ok,
        ];

        // Make the API request to Telegram
        $response = Http::timeout(30)
            ->withHeaders([
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
            ])
            ->post($this->baseUrl.'answerPreCheckoutQuery', $payload);

        // Check if the request was successful
        if (! $response->successful()) {
            Log::error('Telegram answerPreCheckoutQuery API error', [
                'status' => $response->status(),
                'response' => $response->body(),
                'payload' => $payload,
            ]);

            throw new \Exception('Failed to answer pre-checkout query: '.($response->json()['description'] ?? 'Unknown error'));
        }

        $responseData = $response->json();

        // Check if Telegram API returned success
        if (! $responseData['ok']) {
            Log::error('Telegram answerPreCheckoutQuery API returned error', [
                'response' => $responseData,
                'payload' => $payload,
            ]);

            throw new \Exception('Telegram answerPreCheckoutQuery API returned error: '.($responseData['description'] ?? 'Unknown error'));
        }

        return [
            'success' => true,
            'result' => $responseData['result'],
        ];
    }
}
