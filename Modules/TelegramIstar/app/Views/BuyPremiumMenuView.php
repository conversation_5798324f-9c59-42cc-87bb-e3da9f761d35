<?php

namespace Mo<PERSON>les\TelegramIstar\Views;

use App\Helpers\DiscordLogHelper;
use <PERSON><PERSON>les\TelegramBot\Components\BackToLastCommand;
use Modules\TelegramBot\Models\TelegramUser;
use <PERSON><PERSON>les\TelegramClient\Services\TelegramBotClient;
use Mo<PERSON>les\TelegramIstar\Services\ThemeService;
use Mo<PERSON>les\TelegramIstar\Components\GiftForSomeone;
use Mo<PERSON>les\TelegramIstar\Components\BuyForMyself;

class BuyPremiumMenuView implements ViewInterface
{
    protected $themeService;

    public function __construct()
    {
        $this->themeService = new ThemeService;
    }

    /**
     * Handle the buy Telegram Premium menu display
     */
    public function show(string|int $chatId, TelegramUser $user, ?array $params = []): void
    {
        try {
            $botClient = new TelegramBotClient;

            DiscordLogHelper::log('Showing buy Telegram Premium menu to user '.$user->tele_id);

            // Create the buy premium menu description
            $description = $this->getBuyPremiumDescription();

            // Create inline keyboard for buy premium menu
            $keyboard = $this->createBuyPremiumKeyboard($user);

            DiscordLogHelper::log('Sending buy Telegram Premium menu to user '.$user->tele_id);

            $botClient->sendMessage(
                $chatId,
                $description,
                [
                    'reply_markup' => json_encode($keyboard),
                    'parse_mode' => 'Markdown',
                ]
            );
        } catch (\Exception $e) {
            DiscordLogHelper::error('BuyPremiumMenuView failed: '.$e->getMessage());
            DiscordLogHelper::error('Stack trace: '.$e->getTraceAsString());

            $view = new FallbackView;
            $view->show($chatId, $user);
        }
    }

    /**
     * Get the buy Telegram Premium menu description
     */
    private function getBuyPremiumDescription(): string
    {
        return "👑 **Telegram Premium Subscription**\n\n"
            . "Get Telegram Premium at discounted prices!\n\n"
            . "🌟 **Telegram Premium Benefits:**\n"
            . "• Larger file uploads (up to 4GB)\n"
            . "• Faster downloads\n"
            . "• Exclusive stickers and reactions\n"
            . "• Advanced chat management\n"
            . "• Premium app icons and themes\n"
            . "• Voice-to-text conversion\n\n"
            . "Choose how you'd like to purchase Telegram Premium:\n\n";
    }

    /**
     * Create the inline keyboard for buy Telegram Premium menu
     */
    private function createBuyPremiumKeyboard(TelegramUser $user): array
    {
        return [
            'inline_keyboard' => [
                [
                    BuyForMyself::make(['callback_data' => 'show_premium_shop']),
                ],
                [
                    GiftForSomeone::make(['callback_data' => 'buy_premium_for_others']),
                ],
                [
                    BackToLastCommand::make(['user' => $user]),
                ],
            ],
        ];
    }
}
