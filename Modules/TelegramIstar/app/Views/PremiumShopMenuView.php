<?php

namespace Modules\TelegramIstar\Views;

use App\Helpers\DiscordLogHelper;
use App\Helpers\TonHelper;
use Modules\TelegramBot\Models\TelegramUser;
use Mo<PERSON>les\TelegramClient\Services\TelegramBotClient;
use Mo<PERSON>les\TelegramIstar\Services\PremiumService;
use Modules\TelegramIstar\Services\ThemeService;
use Modules\TelegramIstar\Components\BackToMenu;

class PremiumShopMenuView implements ViewInterface
{
    protected $themeService;

    public function __construct()
    {
        $this->themeService = new ThemeService;
    }

    /**
     * Handle the Telegram Premium shop menu display
     */
    public function show(string|int $chatId, TelegramUser $user, ?array $params = []): void
    {
        $username = $params[0] ?? null; // For gifting to others

        try {
            $botClient = new TelegramBotClient;

            DiscordLogHelper::log('Showing Telegram Premium shop menu to user '.$user->tele_id);

            $description = $this->getPremiumShopDescription($username);

            $keyboard = $this->createPremiumShopKeyboard($username);

            DiscordLogHelper::log('Sending Telegram Premium shop menu to user '.$user->tele_id);

            $botClient->sendMessage(
                $chatId,
                $description,
                [
                    'reply_markup' => json_encode($keyboard),
                    'parse_mode' => 'Markdown',
                ]
            );
        } catch (\Exception $e) {
            DiscordLogHelper::error('PremiumShopMenuView failed: '.$e->getMessage());
            DiscordLogHelper::error('Stack trace: '.$e->getTraceAsString());

            $view = new FallbackView;
            $view->show($chatId, $user);
        }
    }

    /**
     * Get the Telegram Premium shop description
     */
    private function getPremiumShopDescription(?string $username): string
    {
        $tonHelper = new TonHelper;
        $tonRate = $tonHelper->fetchTonPrice();

        $header = $username
            ? "🎁 **Telegram Premium Gift Shop**\n👤 Recipient: @{$username}\n\n"
            : "👑 **Telegram Premium Shop**\n\n";

        $description = $header
            . "💎 Live TON price: \${$tonRate}\n\n"
            . "🌟 **Telegram Premium Benefits:**\n"
            . "• Larger file uploads (up to 4GB)\n"
            . "• Faster downloads & priority support\n"
            . "• Exclusive stickers, reactions & themes\n"
            . "• Advanced chat management tools\n"
            . "• Voice-to-text conversion\n\n"
            . "📦 **Available Packages (Discounted Prices):**\n\n";

        return $description;
    }

    /**
     * Create the inline keyboard for Telegram Premium shop menu
     */
    private function createPremiumShopKeyboard(?string $username): array
    {
        $inlineKeyboard = [];

        $tonRate = (new TonHelper)->fetchTonPrice();
        $premiumService = app(PremiumService::class);
        $premiumPackages = $premiumService->getPremiumPackages();

        // Create package buttons
        foreach ($premiumPackages as $package) {
            $tonPrice = number_format($package['usd_price'] / $tonRate, 4);
            $periodText = $premiumService->getPeriodText($package['days']);
            $buttonText = "👑 {$periodText} - {$tonPrice} TON (\${$package['usd_price']})";

            $callbackData = 'buy_premium-'.$package['days'];
            if ($username) {
                $callbackData .= '-'.$username;
            }
            $inlineKeyboard[] = [
                [
                    'text' => $buttonText,
                    'callback_data' => $callbackData,
                ],
            ];
        }

        // Add back button
        $inlineKeyboard[] = [
            BackToMenu::make(),
        ];

        return ['inline_keyboard' => $inlineKeyboard];
    }
}
