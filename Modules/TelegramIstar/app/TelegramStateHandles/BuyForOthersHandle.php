<?php

namespace Modules\TelegramIstar\TelegramStateHandles;

use App\Helpers\DiscordLogHelper;
use Mo<PERSON>les\TelegramBot\Components\BackToLastCommand;
use <PERSON><PERSON>les\TelegramBot\Interfaces\StateHandleInterface;
use Mo<PERSON>les\TelegramBot\Models\TelegramUser;
use Mo<PERSON>les\TelegramClient\Services\TelegramBotClient;
use Modules\TelegramBot\Services\MessageStateService;

class BuyForOthersHandle implements StateHandleInterface
{
    /**
     * Handle the username processing for buying stars for others
     */
    public function handle(string $text, string|int $chatId, TelegramUser $user): void
    {
        try {
            $username = trim($text);

            if (!$username) {
                $this->sendErrorMessage($chatId, 'Please provide a valid username.');
                return;
            }

            // Validate username format
            if (!$username) {
                $this->sendInvalidUsernameMessage($chatId);
                return;
            }

            // Remove @ symbol if present
            $username = ltrim($username, '@');

            DiscordLogHelper::log("Processing buy stars for others - username: {$username} for user {$user->tele_id}");

            $this->showStarShopForUser($chatId, $user, $username);

        } catch (\Exception $e) {
            DiscordLogHelper::error('BuyForOthersUsernameHandle failed: '.$e->getMessage());
            DiscordLogHelper::error('Stack trace: '.$e->getTraceAsString());

            $this->sendErrorMessage($chatId, 'An error occurred while processing your request.');
        }
    }

    /**
     * Show star shop for buying for specific user
     */
    private function showStarShopForUser(string|int $chatId, TelegramUser $user, string $targetUsername): void
    {
        try {
            $botClient = new TelegramBotClient;

            // Create description with target username
            $description = $this->getStarShopDescription($targetUsername);

            // Create keyboard with modified callback data to include target username
            $keyboard = $this->createStarShopKeyboard($targetUsername, $user);

            $botClient->sendMessage(
                $chatId,
                $description,
                [
                    'reply_markup' => json_encode($keyboard),
                    'parse_mode' => 'Markdown',
                ]
            );

            // Clear user state
            $stateService = new MessageStateService();
            $stateService->clearUserState($user);

        } catch (\Exception $e) {
            DiscordLogHelper::error('Failed to show star shop for user: '.$e->getMessage());
            $this->sendErrorMessage($chatId, 'Failed to process your request. Please try again.');
        }
    }

    /**
     * Get the star shop description for buying for others
     */
    private function getStarShopDescription(string $targetUsername): string
    {
        $tonHelper = new \App\Helpers\TonHelper;
        $tonRate = $tonHelper->fetchTonPrice();

        return "🎁 **Buy Stars for @{$targetUsername}** 🎁\n\n"
            ."💰 Choose a Star Package:\n\n"
            .'💎 Current TON Rate: $'.number_format($tonRate, 4)."\n"
            ."🔥 Payment method: TON only\n\n"
            .'Select a package below:';
    }

    /**
     * Create the inline keyboard for star shop when buying for others
     */
    private function createStarShopKeyboard(string $targetUsername, TelegramUser $user): array
    {
        $inlineKeyboard = [];

        $tonHelper = new \App\Helpers\TonHelper;
        $tonRate = $tonHelper->fetchTonPrice();
        $starShopService = app(\Modules\TelegramIstar\Services\StarShopService::class);
        $starPackages = $starShopService->getStarPackages();

        // Create package buttons with target username in callback data
        foreach ($starPackages as $package) {
            $tonPrice = number_format($package['usd_price'] / $tonRate, 4);
            $buttonText = $package['stars'].'⭐ - '.$tonPrice.' TON ($'.$package['usd_price'].')';

            $inlineKeyboard[] = [
                [
                    'text' => $buttonText,
                    'callback_data' => 'buy_stars-'.$package['stars'].'_'.$targetUsername,
                ],
            ];
        }

        // Add cancel button
        $inlineKeyboard[] = [
            BackToLastCommand::make([
                'user' => $user,
            ]),
        ];

        return [
            'inline_keyboard' => $inlineKeyboard,
        ];
    }

    /**
     * Send invalid username message
     */
    private function sendInvalidUsernameMessage(string|int $chatId): void
    {
        $botClient = new TelegramBotClient;

        $message = "❌ **Invalid Format!**\n\n"
            ."Please use the correct username format:\n"
            ."**@username**\n\n"
            ."*Example: @johndoe*\n\n";

        $botClient->sendMessage(
            $chatId,
            $message,
            [
                'parse_mode' => 'Markdown',
            ]
        );
    }

    /**
     * Send error message
     */
    private function sendErrorMessage(string|int $chatId, string $errorMessage): void
    {
        $botClient = new TelegramBotClient;

        $botClient->sendMessage(
            $chatId,
            '❌ '.$errorMessage,
            [
                'parse_mode' => 'Markdown',
            ]
        );
    }
}
