<?php

namespace Modules\TelegramIstar\Observers;

use Mo<PERSON>les\TelegramIstar\Models\WidthDrawl;
use Modules\TelegramIstar\Services\AdminNotificationService;
use Illuminate\Support\Facades\Log;

class WidthDrawlObserver
{
    protected AdminNotificationService $notificationService;

    public function __construct(AdminNotificationService $notificationService)
    {
        $this->notificationService = $notificationService;
    }

    /**
     * Handle the WidthDrawl "created" event.
     */
    public function created(WidthDrawl $withdrawal): void
    {
        try {
            // Send admin notification for all withdrawal types
            $this->notificationService->sendWithdrawalNotification($withdrawal);

            Log::info('Admin notification sent for withdrawal', [
                'withdrawal_id' => $withdrawal->id,
                'type' => $withdrawal->type,
                'user_id' => $withdrawal->user_id,
                'amount' => $withdrawal->amount
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to send admin notification for withdrawal', [
                'withdrawal_id' => $withdrawal->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * Handle the WidthDrawl "updated" event.
     */
    public function updated(WidthDrawl $withdrawal): void {}

    /**
     * Handle the WidthDrawl "deleted" event.
     */
    public function deleted(WidthDrawl $withdrawal): void {}

    /**
     * Handle the WidthDrawl "restored" event.
     */
    public function restored(WidthDrawl $withdrawal): void {}

    /**
     * Handle the WidthDrawl "force deleted" event.
     */
    public function forceDeleted(WidthDrawl $withdrawal): void {}
}
