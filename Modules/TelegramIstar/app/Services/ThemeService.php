<?php

namespace Modules\TelegramIstar\Services;

use App\Services\SettingsService;

class ThemeService
{
    /**
     * Get all available themes
     */
    public function getAllThemes(): array
    {
        return [
            'original' => [
                'name' => 'StarWallet Classic',
                'description_template' => "🚀✨ *Welcome to StarWallet!* ✨🚀\n\n".
                    "🔹 *Your Balance:* ⭐ {balance}\n".
                    "🔹 *Available Withdrawal:* ⭐ {available}\n".
                    "🔸 *VIP Status:* {vip_emoji} VIP {vip_level}\n\n".
                    '_Choose your next step wisely!_ 💡',
                'buttons' => [
                    [
                        ['text' => '➕ Add Funds', 'type' => 'callback', 'data' => 'show_deposit'],
                        ['text' => '💸 Withdraw', 'type' => 'callback', 'data' => 'withdraw'],
                    ],
                    [
                        ['text' => '🌟 Buy Stars 🌟', 'type' => 'callback', 'data' => 'show_buy_stars_menu'],
                        ['text' => '🏆 Buy Telegram Premium 🏆', 'type' => 'callback', 'data' => 'show_buy_premium_menu'],
                    ],
                    [
                        ['text' => '👑 VIP Zone', 'type' => 'callback', 'data' => 'vip_status'],
                        ['text' => '🎁 Referral', 'type' => 'callback', 'data' => 'referral'],
                    ],
                    [
                        ['text' => '📋 Waiting List', 'type' => 'callback', 'data' => 'waitlist'],
                        ['text' => '💼 TON Wallet', 'type' => 'callback', 'data' => 'manage_wallet'],
                    ],
                    [
                        ['text' => '🎧 Support 24/7', 'type' => 'callback', 'data' => 'contact'],
                        ['text' => '📣 Official Channel', 'type' => 'url', 'data' => $this->getOfficialChannelUrl()],
                    ],
                ],
            ],
            'rocket' => [
                'name' => 'Galactic Hub',
                'description_template' => "🌌 *Welcome to the Galactic Hub!* 🌌\n\n".
                    "🔹 *Star Credits:* ⭐ {balance}\n".
                    "🔹 *Withdrawable Credits:* ⭐ {available}\n".
                    "🔸 *Commander Rank:* {vip_emoji} Level {vip_level}\n\n".
                    '_Navigate through the stars!_ 🚀',
                'buttons' => [
                    [
                        ['text' => '🚀 Add Fuel', 'type' => 'callback', 'data' => 'show_deposit'],
                        ['text' => '🛸 Docking Bay', 'type' => 'callback', 'data' => 'withdraw'],
                    ],
                    [
                        ['text' => '🌠 Star Shop', 'type' => 'callback', 'data' => 'show_buy_stars_menu'],
                        ['text' => '🪐 Buy Premium', 'type' => 'callback', 'data' => 'show_buy_premium_menu'],
                    ],
                    [
                        ['text' => '👩‍🚀 VIP Launchpad', 'type' => 'callback', 'data' => 'vip_status'],
                        ['text' => '🌌 Crew Invites', 'type' => 'callback', 'data' => 'referral'],
                    ],
                    [
                        ['text' => '🔭 Launch Queue', 'type' => 'callback', 'data' => 'waitlist'],
                        ['text' => '💰 TON Galactic Vault', 'type' => 'callback', 'data' => 'manage_wallet'],
                    ],
                    [
                        ['text' => '📡 Mission Control', 'type' => 'callback', 'data' => 'contact'],
                        ['text' => '🛰 Official Comms', 'type' => 'url', 'data' => $this->getOfficialChannelUrl()],
                    ],
                ],
            ],
            'halloween' => [
                'name' => 'Haunted StarWallet',
                'description_template' => "👻 *Welcome to the Haunted StarWallet!* 👻\n\n".
                    "🔹 *Your Treats:* ⭐ {balance}\n".
                    "🔹 *Withdrawable Treats:* ⭐ {available}\n".
                    "🔸 *VIP Rank:* {vip_emoji} Level {vip_level}\n\n".
                    '_Beware of the shadows!_ 🦇',
                'buttons' => [
                    [
                        ['text' => '🎃 Add Treats', 'type' => 'callback', 'data' => 'show_deposit'],
                        ['text' => '🦇 Cash Out', 'type' => 'callback', 'data' => 'withdraw'],
                    ],
                    [
                        ['text' => '🔮 Star Shop', 'type' => 'callback', 'data' => 'show_buy_stars_menu'],
                        ['text' => '🧪 Premium Elixir', 'type' => 'callback', 'data' => 'show_buy_premium_menu'],
                    ],
                    [
                        ['text' => '🧛 VIP Lounge', 'type' => 'callback', 'data' => 'vip_status'],
                    ],
                    [
                        ['text' => '🧟 Summon Friends', 'type' => 'callback', 'data' => 'referral'],
                        ['text' => '👻 Waiting Souls', 'type' => 'callback', 'data' => 'waitlist'],
                    ],
                    [
                        ['text' => '⚰️ TON Spooky Wallet', 'type' => 'callback', 'data' => 'manage_wallet'],
                    ],
                    [
                        ['text' => '🕯 Support', 'type' => 'callback', 'data' => 'contact'],
                        ['text' => '🕸 Official Channel', 'type' => 'url', 'data' => $this->getOfficialChannelUrl()],
                    ],
                ],
            ],
            'luxury' => [
                'name' => 'Elite StarWallet Club',
                'description_template' => "💎 *Welcome to the Elite StarWallet Club!* 💎\n\n".
                    "🔹 *Your Fortune:* ⭐ {balance}\n".
                    "🔹 *Available to Cash Out:* ⭐ {available}\n".
                    "🔸 *VIP Tier:* {vip_emoji} Level {vip_level}\n\n".
                    '_Experience the luxury!_ 🥂',
                'buttons' => [
                    [
                        ['text' => '💰 Deposit', 'type' => 'callback', 'data' => 'show_deposit'],
                        ['text' => '💸 Cash Out', 'type' => 'callback', 'data' => 'withdraw'],
                        ['text' => '💳 TON Wallet', 'type' => 'callback', 'data' => 'manage_wallet'],
                    ],
                    [
                        ['text' => '⭐ Buy Stars', 'type' => 'callback', 'data' => 'show_buy_stars_menu'],
                        ['text' => '💎 Premium', 'type' => 'callback', 'data' => 'show_buy_premium_menu'],
                        ['text' => '👑 VIP Club', 'type' => 'callback', 'data' => 'vip_status'],
                    ],
                    [
                        ['text' => '💌 Invite Friends', 'type' => 'callback', 'data' => 'referral'],
                        ['text' => '⏳ Waitlist', 'type' => 'callback', 'data' => 'waitlist'],
                    ],
                    [
                        ['text' => '🛎 Support', 'type' => 'callback', 'data' => 'contact'],
                        ['text' => '📣 Official Channel', 'type' => 'url', 'data' => $this->getOfficialChannelUrl()],
                    ],
                ],
            ],
            'mario' => [
                'name' => 'Mushroom Kingdom',
                'description_template' => "🍄 *Welcome to the Mushroom Kingdom!* 🍄\n\n".
                    "🔹 *Your Coins:* ⭐ {balance}\n".
                    "🔹 *Mushrooms to Withdraw:* ⭐ {available}\n".
                    "🔸 *VIP Castle Level:* {vip_emoji} {vip_level}\n\n".
                    '_Choose your adventure!_ 🎮',
                'buttons' => [
                    [
                        ['text' => '🪙 Add Coins', 'type' => 'callback', 'data' => 'show_deposit'],
                        ['text' => '💰 Withdraw Mushrooms', 'type' => 'callback', 'data' => 'withdraw'],
                    ],
                    [
                        ['text' => '⭐ Power-Up Stars', 'type' => 'callback', 'data' => 'show_buy_stars_menu'],
                        ['text' => '🍒 Premium Coin Box', 'type' => 'callback', 'data' => 'show_buy_premium_menu'],
                    ],
                    [
                        ['text' => '👑 VIP Castle', 'type' => 'callback', 'data' => 'vip_status'],
                        ['text' => '🎁 Invite Goombas', 'type' => 'callback', 'data' => 'referral'],
                    ],
                    [
                        ['text' => '🎮 Waiting Warp Zone', 'type' => 'callback', 'data' => 'waitlist'],
                        ['text' => '🧳 TON Mushroom Vault', 'type' => 'callback', 'data' => 'manage_wallet'],
                    ],
                    [
                        ['text' => '🚪 24/7 Help Block', 'type' => 'callback', 'data' => 'contact'],
                        ['text' => '📣 Official Channel', 'type' => 'url', 'data' => $this->getOfficialChannelUrl()],
                    ],
                ],
            ],
            'neon' => [
                'name' => 'Neon Nexus',
                'description_template' => "💠 *Welcome to the Neon Nexus!* 💠\n\n".
                    "🔹 *Neon Credits:* ⭐ {balance}\n".
                    "🔹 *Transferable Credits:* ⭐ {available}\n".
                    "🔸 *VIP Matrix Level:* {vip_emoji} {vip_level}\n\n".
                    '_Enter the cyberworld!_ 🔮',
                'buttons' => [
                    [
                        ['text' => '💠 Neon Recharge', 'type' => 'callback', 'data' => 'show_deposit'],
                        ['text' => '⚡ Cash Transfer', 'type' => 'callback', 'data' => 'withdraw'],
                    ],
                    [
                        ['text' => '🌌 Neon Stars', 'type' => 'callback', 'data' => 'show_buy_stars_menu'],
                        ['text' => '🔮 Premium Nexus', 'type' => 'callback', 'data' => 'show_buy_premium_menu'],
                    ],
                    [
                        ['text' => '👾 VIP Matrix', 'type' => 'callback', 'data' => 'vip_status'],
                        ['text' => '🔑 Invite Hackers', 'type' => 'callback', 'data' => 'referral'],
                    ],
                    [
                        ['text' => '🛸 Waiting Zone', 'type' => 'callback', 'data' => 'waitlist'],
                        ['text' => '🖥️ TON Cyber Wallet', 'type' => 'callback', 'data' => 'manage_wallet'],
                    ],
                    [
                        ['text' => '📡 Tech Support', 'type' => 'callback', 'data' => 'contact'],
                        ['text' => '🔷 Official Portal', 'type' => 'url', 'data' => $this->getOfficialChannelUrl()],
                    ],
                ],
            ],
            'retro' => [
                'name' => 'Retro Arcade',
                'description_template' => "🕹️ *Welcome to the Retro Arcade!* 🕹️\n\n".
                    "🔹 *Your Credits:* ⭐ {balance}\n".
                    "🔹 *Points to Withdraw:* ⭐ {available}\n".
                    "🔸 *VIP High Score:* {vip_emoji} Level {vip_level}\n\n".
                    '_Play to win!_ 🎲',
                'buttons' => [
                    [
                        ['text' => '💿 Add Credits', 'type' => 'callback', 'data' => 'show_deposit'],
                        ['text' => '🎮 Withdraw Points', 'type' => 'callback', 'data' => 'withdraw'],
                    ],
                    [
                        ['text' => '🕹️ Star Arcade', 'type' => 'callback', 'data' => 'show_buy_stars_menu'],
                        ['text' => '📼 Premium Tape', 'type' => 'callback', 'data' => 'show_buy_premium_menu'],
                    ],
                    [
                        ['text' => '👑 VIP High Score', 'type' => 'callback', 'data' => 'vip_status'],
                        ['text' => '🎲 Invite Players', 'type' => 'callback', 'data' => 'referral'],
                    ],
                    [
                        ['text' => '📀 Waiting Zone', 'type' => 'callback', 'data' => 'waitlist'],
                        ['text' => '💸 TON Wallet Credits', 'type' => 'callback', 'data' => 'manage_wallet'],
                    ],
                    [
                        ['text' => '📞 24/7 Helpdesk', 'type' => 'callback', 'data' => 'contact'],
                        ['text' => '🖥 Official Channel', 'type' => 'url', 'data' => $this->getOfficialChannelUrl()],
                    ],
                ],
            ],
            'christmas' => [
                'name' => 'North Pole',
                'description_template' => "🎄 *Welcome to the North Pole!* 🎄\n\n".
                    "🔹 *Your Gifts:* ⭐ {balance}\n".
                    "🔹 *Withdrawable Gifts:* ⭐ {available}\n".
                    "🔸 *VIP Sleigh Level:* {vip_emoji} {vip_level}\n\n".
                    '_Spread the holiday cheer!_ 🎅',
                'buttons' => [
                    [
                        ['text' => '🎄 Add Gifts', 'type' => 'callback', 'data' => 'show_deposit'],
                        ['text' => '🎁 Cash Out', 'type' => 'callback', 'data' => 'withdraw'],
                    ],
                    [
                        ['text' => '❄️ Star Wonderland', 'type' => 'callback', 'data' => 'show_buy_stars_menu'],
                        ['text' => '🎅 Premium Claus', 'type' => 'callback', 'data' => 'show_buy_premium_menu'],
                    ],
                    [
                        ['text' => '👑 VIP Sleigh', 'type' => 'callback', 'data' => 'vip_status'],
                        ['text' => '🦌 Refer a Reindeer', 'type' => 'callback', 'data' => 'referral'],
                    ],
                    [
                        ['text' => '🛷 Waiting Snow', 'type' => 'callback', 'data' => 'waitlist'],
                        ['text' => '💳 TON Santa\'s Wallet', 'type' => 'callback', 'data' => 'manage_wallet'],
                    ],
                    [
                        ['text' => '🎶 Jingle Support', 'type' => 'callback', 'data' => 'contact'],
                        ['text' => '📢 Official Channel', 'type' => 'url', 'data' => $this->getOfficialChannelUrl()],
                    ],
                ],
            ],
        ];
    }

    /**
     * Get a specific theme by name
     */
    public function getTheme(string $themeName): ?array
    {
        $themes = $this->getAllThemes();

        return $themes[$themeName] ?? null;
    }

    /**
     * Get current theme (can be based on user preference, time, etc.)
     */
    public function getCurrentTheme(?string $preferredTheme = null): array
    {
        // Priority: user preference > environment > seasonal > default
        $themeName = $preferredTheme
            ?? env('TELEGRAM_ISTAR_CURRENT_THEME')
            ?? $this->getSeasonalTheme()
            ?? 'original';

        $theme = $this->getTheme($themeName);

        // Fallback to original if theme not found
        return $theme ?? $this->getTheme('original');
    }

    /**
     * Get seasonal theme based on current date
     */
    private function getSeasonalTheme(): ?string
    {
        $currentMonth = now()->month;
        $currentDay = now()->day;

        // Christmas season (December)
        if ($currentMonth === 12) {
            return 'christmas';
        }

        // Halloween season (October)
        if ($currentMonth === 10) {
            return 'halloween';
        }

        // No seasonal theme
        return null;
    }

    /**
     * Get list of available theme names
     */
    public function getAvailableThemes(): array
    {
        return array_keys($this->getAllThemes());
    }

    /**
     * Get theme names with descriptions
     */
    public function getThemeList(): array
    {
        $themes = $this->getAllThemes();
        $themeList = [];

        foreach ($themes as $key => $theme) {
            $themeList[$key] = $theme['name'];
        }

        return $themeList;
    }

    /**
     * Check if theme exists
     */
    public function themeExists(string $themeName): bool
    {
        return $this->getTheme($themeName) !== null;
    }

    /**
     * Get official channel URL
     */
    private function getOfficialChannelUrl(): string
    {
        return (new SettingsService)->getSetting('telegram_isar_official_channel');
    }

    /**
     * Format description template with user data
     */
    public function formatDescription(string $template, array $userData): string
    {
        return str_replace(
            ['{balance}', '{available}', '{vip_emoji}', '{vip_level}', '{balance_ton}'],
            [
                number_format($userData['balance'], 2),
                number_format($userData['available'], 2),
                $userData['vip_emoji'],
                $userData['vip_level'],
                number_format($userData['balance_ton'] ?? 0, 4),
            ],
            $template
        );
    }

    /**
     * Create inline keyboard from button configuration
     */
    public function createInlineKeyboard(array $buttonRows): array
    {
        $keyboard = [
            'inline_keyboard' => [],
        ];

        foreach ($buttonRows as $row) {
            $rowButtons = [];

            foreach ($row as $btn) {
                if ($btn['type'] === 'callback') {
                    $rowButtons[] = [
                        'text' => $btn['text'],
                        'callback_data' => $btn['data'],
                    ];
                } elseif ($btn['type'] === 'url') {
                    $rowButtons[] = [
                        'text' => $btn['text'],
                        'url' => $btn['data'],
                    ];
                }
            }

            $keyboard['inline_keyboard'][] = $rowButtons;
        }

        return $keyboard;
    }
}
