<?php

namespace Modules\TelegramIstar\Services;

use App\Services\SettingsService;
use App\Helpers\TonHelper;

class PremiumService
{
    /**
     * Telegram Premium packages mapping (days => USD price)
     * Cheaper prices compared to official Telegram Premium
     * Based on the plan document requirements
     */
    public const PREMIUM_PACKAGES = [
        90 => 14,   // 3 months - cheaper than official
        180 => 19,  // 6 months - cheaper than official
        365 => 33,  // 1 year - cheaper than official
    ];

    /**
     * Get all Telegram Premium packages sorted by duration
     */
    public function getPremiumPackages(): array
    {
        $packages = [];

        foreach (self::PREMIUM_PACKAGES as $days => $usdPrice) {
            $packages[] = [
                'days' => $days,
                'usd_price' => $usdPrice,
            ];
        }

        return $packages;
    }

    /**
     * Calculate TON price for a given Telegram Premium package
     */
    public function calculateTonPrice(int $days): float
    {
        $tonHelper = new TonHelper;
        $tonRate = $tonHelper->fetchTonPrice();

        $usdPrice = $this->calculateUsdPrice($days);
        $tonPrice = $usdPrice / $tonRate;

        return $tonPrice;
    }

    /**
     * Calculate USD price for Telegram Premium package
     */
    public function calculateUsdPrice(int $days): float
    {
        return self::PREMIUM_PACKAGES[$days] ?? 0;
    }

    /**
     * Format TON price for display
     */
    public function tonPriceForView(int $days): string
    {
        $tonPrice = $this->calculateTonPrice($days);
        return number_format($tonPrice, 4);
    }

    /**
     * Get Telegram Premium package by days
     */
    public function getPackageByDays(int $days): ?array
    {
        if (!isset(self::PREMIUM_PACKAGES[$days])) {
            return null;
        }

        return [
            'days' => $days,
            'usd_price' => self::PREMIUM_PACKAGES[$days],
        ];
    }

    /**
     * Check if Telegram Premium package exists
     */
    public function isValidPackage(int $days): bool
    {
        return isset(self::PREMIUM_PACKAGES[$days]);
    }

    /**
     * Get period text for display (e.g., "3 months", "1 year")
     */
    public function getPeriodText(int $days): string
    {
        return match ($days) {
            90 => '3 months',
            180 => '6 months',
            365 => '1 year',
            default => $days . ' days',
        };
    }
}
