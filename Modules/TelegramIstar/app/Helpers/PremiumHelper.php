<?php

namespace Modules\TelegramIstar\Helpers;

use Mo<PERSON>les\TelegramBot\Models\TelegramUser;
use Mo<PERSON>les\TelegramIstar\Models\WidthDrawl;

class PremiumHelper
{

    /**
     * Build Telegram Premium purchase metadata for transaction
     */
    public function buildPremiumPurchaseMetaData(int $days, ?string $targetUsername = null): array
    {
        $metadata = [
            'type' => 'telegram_premium_purchase',
            'days' => $days,
        ];

        if ($targetUsername) {
            $metadata['target_username'] = $targetUsername;
        }

        return $metadata;
    }

    /**
     * Process Telegram Premium purchase after payment verification
     */
    public function processPurchase(TelegramUser $user, array $payload): void
    {
        $type = $payload['type'] ?? null;

        switch ($type) {
            case 'telegram_premium_purchase':
                $days = $payload['days'] ?? 0;
                $targetUsername = $payload['target_username'] ?? null;

                if ($days > 0) {
                    if ($targetUsername) {
                        // Create withdrawal request for gifting Telegram Premium to others
                        WidthDrawl::create([
                            'user_id' => $user->id,
                            'status' => 'pending',
                            'amount' => $days,
                            'currency' => 'TELEGRAM_PREMIUM',
                            'address' => '@' . $targetUsername,
                            'type' => 'telegram_premium_gift',
                        ]);
                    } else {
                        // Create withdrawal request for user's own Telegram Premium
                        WidthDrawl::create([
                            'user_id' => $user->id,
                            'status' => 'pending',
                            'amount' => $days,
                            'currency' => 'TELEGRAM_PREMIUM',
                            'address' => '@' . ($user->username ?: "user{$user->tele_id}"),
                            'type' => 'telegram_premium_purchase',
                        ]);
                    }
                }
                break;
            default:
                // Unknown type, do nothing
                break;
        }
    }
}
