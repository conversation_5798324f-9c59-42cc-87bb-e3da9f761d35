<?php

namespace Modules\TelegramIstar\Helpers;

use Modules\TelegramBot\Models\TelegramUser;
use Modules\TelegramIstar\Models\WidthDrawl;
use Carbon\Carbon;

class PremiumHelper
{
    /**
     * Get premium information for a user
     */
    public function getPremiumInfo(TelegramUser $user): array
    {
        $isPremium = $this->isPremiumActive($user);
        $expiresAt = $user->premium_expires_at;
        $premiumType = $user->premium_type;

        $daysRemaining = 0;
        if ($isPremium && $expiresAt) {
            $daysRemaining = Carbon::parse($expiresAt)->diffInDays(now(), false);
            $daysRemaining = max(0, -$daysRemaining); // Ensure positive value
        }

        return [
            'is_premium' => $isPremium,
            'expires_at' => $expiresAt,
            'premium_type' => $premiumType,
            'days_remaining' => $daysRemaining,
            'premium_emoji' => $isPremium ? '👑' : '👤',
        ];
    }

    /**
     * Check if user has active premium subscription
     */
    public function isPremiumActive(TelegramUser $user): bool
    {
        if (!$user->premium_expires_at) {
            return false;
        }

        return Carbon::parse($user->premium_expires_at)->isFuture();
    }

    /**
     * Build premium purchase metadata for transaction
     */
    public function buildPremiumPurchaseMetaData(int $days, ?string $targetUsername = null): array
    {
        $metadata = [
            'type' => 'premium_purchase',
            'days' => $days,
        ];

        if ($targetUsername) {
            $metadata['target_username'] = $targetUsername;
        }

        return $metadata;
    }

    /**
     * Process premium purchase after payment verification
     */
    public function processPurchase(TelegramUser $user, array $payload): void
    {
        $type = $payload['type'] ?? null;
        
        switch ($type) {
            case 'premium_purchase':
                $days = $payload['days'] ?? 0;
                $targetUsername = $payload['target_username'] ?? null;

                if ($days > 0) {
                    if ($targetUsername) {
                        // Create withdrawal request for gifting premium to others
                        WidthDrawl::create([
                            'user_id' => $user->id,
                            'status' => 'pending',
                            'amount' => $days,
                            'currency' => 'PREMIUM',
                            'address' => '@' . $targetUsername,
                            'type' => 'premium_gift',
                        ]);
                    } else {
                        // Add premium time to the user
                        $this->addPremiumTime($user, $days);
                    }
                }
                break;
            default:
                // Unknown type, do nothing
                break;
        }
    }

    /**
     * Add premium time to user account
     */
    public function addPremiumTime(TelegramUser $user, int $days, string $premiumType = 'standard'): void
    {
        $currentExpiration = $user->premium_expires_at;
        
        // If user already has premium, extend from current expiration
        // Otherwise, start from now
        if ($currentExpiration && Carbon::parse($currentExpiration)->isFuture()) {
            $newExpiration = Carbon::parse($currentExpiration)->addDays($days);
        } else {
            $newExpiration = now()->addDays($days);
        }

        $user->update([
            'premium_expires_at' => $newExpiration,
            'premium_type' => $premiumType,
        ]);
    }

    /**
     * Get premium status emoji
     */
    public function getPremiumEmoji(TelegramUser $user): string
    {
        return $this->isPremiumActive($user) ? '👑' : '👤';
    }

    /**
     * Get premium status text
     */
    public function getPremiumStatusText(TelegramUser $user): string
    {
        if (!$this->isPremiumActive($user)) {
            return 'Free User';
        }

        $info = $this->getPremiumInfo($user);
        $daysRemaining = $info['days_remaining'];
        
        if ($daysRemaining > 30) {
            $months = floor($daysRemaining / 30);
            return "Premium ({$months} months left)";
        } elseif ($daysRemaining > 0) {
            return "Premium ({$daysRemaining} days left)";
        } else {
            return "Premium (expires today)";
        }
    }
}
