<?php

namespace Modules\TelegramIstar\TelegramCommands;

use <PERSON><PERSON><PERSON>\TelegramBot\Models\TelegramUser;
use Mo<PERSON>les\TelegramBot\TelegramCommands\CommandInterface;
use Mo<PERSON>les\TelegramIstar\Views\StarShopMenuView;

class ShowStarShopCommand implements CommandInterface
{
    public function handle(string|int $chatId, TelegramUser $user, ?array $params = []): array
    {
        //gift case
        $username = $params[0] ?? null;

        $view = new StarShopMenuView;
        $view->show($chatId, $user, $username);

        return ['success' => true, 'handled' => true];
    }
}
