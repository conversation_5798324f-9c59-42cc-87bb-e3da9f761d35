<?php

namespace Modules\TelegramIstar\TelegramCommands;

use App\Helpers\DiscordLogHelper;
use Modules\TelegramBot\Components\BackToLastCommand;
use <PERSON><PERSON>les\TelegramBot\Models\TelegramUser;
use Mo<PERSON>les\TelegramBot\TelegramCommands\CommandInterface;
use Modules\TelegramClient\Services\TelegramBotClient;
use Modules\TelegramBot\Services\MessageStateService;

class BuyForOthersCommand implements CommandInterface
{
    /**
     * Handle the buy for others command (callback from button)
     */
    public function handle(string|int $chatId, TelegramUser $user, ?array $params = []): array
    {
        $type = $params[0] ?? null;
        if (!$type || $type !== 'star' || $type !== 'premium') {
            DiscordLogHelper::error('Invalid type for BuyForOthersCommand: ' . $type);
            return ['success' => false, 'handled' => false];
        }

        try {
            $botClient = new TelegramBotClient;

            DiscordLogHelper::log('Showing buy for others prompt to user '.$user->tele_id);

            $message = $this->getBuyForOthersMessage();
            $keyboard = $this->createBackKeyboard($user);

            $botClient->sendMessage(
                $chatId,
                $message,
                [
                    'reply_markup' => json_encode($keyboard),
                    'parse_mode' => 'Markdown',
                ]
            );

            // Store user state for next message handling
            $messageStateService = new MessageStateService;
            $messageStateService->setUserState($user, 'buy_for_others_username');

            return ['success' => true, 'handled' => true];
        } catch (\Exception $e) {
            DiscordLogHelper::error('BuyForOthersCommand failed: '.$e->getMessage());
            DiscordLogHelper::error('Stack trace: '.$e->getTraceAsString());

            return ['success' => false, 'handled' => false];
        }
    }

    /**
     * Get the buy stars for others message
     */
    private function getBuyForOthersMessage(): string
    {
        return "🎁 **Buy for Others** 🎁\n\n"
            ."Please send the Telegram username of the person you want to buy for.\n\n"
            ."**Format:** @username\n\n"
            ."*Example: @johndoe*";
    }

    /**
     * Create the back keyboard
     */
    private function createBackKeyboard(TelegramUser $user): array
    {
        return [
            'inline_keyboard' => [
                [
                    BackToLastCommand::make([
                        'user' => $user
                    ])
                ],
            ],
        ];
    }
}
