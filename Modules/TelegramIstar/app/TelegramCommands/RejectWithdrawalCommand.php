<?php

namespace Modules\TelegramIstar\TelegramCommands;

use App\Helpers\DiscordLogHelper;
use Mo<PERSON>les\TelegramBot\Models\TelegramUser;
use Modules\TelegramBot\Services\MessageContextService;
use Mo<PERSON>les\TelegramBot\TelegramCommands\AdminMiddleware;
use Mo<PERSON>les\TelegramBot\TelegramCommands\CommandInterface;
use Modules\TelegramClient\Services\TelegramBotClient;
use Mo<PERSON>les\TelegramTonPayment\Models\VerificationTransaction;

class RejectWithdrawalCommand implements CommandInterface
{
    public function handle(string|int $chatId, TelegramUser $user, ?array $params = []): array
    {
        return AdminMiddleware::check($user, $chatId, function () use ($chatId, $user, $params) {
            $withdrawalId = $params[0] ?? null;
            if (! $withdrawalId) {
                DiscordLogHelper::error('RejectWithdrawal: Missing withdrawal ID in parameters');

                return ['success' => false, 'handled' => false];
            }

            try {
                VerificationTransaction::where('id', $withdrawalId)
                    ->update(['status' => 'rejected']);

                $botClient = new TelegramBotClient;

                DiscordLogHelper::log('Rejecting withdrawal ID '.$withdrawalId.' by admin '.$user->tele_id);

                $message = "❌ Withdrawal ID {$withdrawalId} has been rejected.";
                $botClient->sendMessage(
                    $chatId,
                    $message,
                    [
                        'parse_mode' => 'Markdown',
                    ]
                );

                // Delete the original notification message
                MessageContextService::deleteOriginalMessage($botClient, $chatId);

            } catch (\Exception $e) {
                DiscordLogHelper::error('RejectWithdrawal failed: '.$e->getMessage());
                DiscordLogHelper::error('Stack trace: '.$e->getTraceAsString());

                return ['success' => false, 'handled' => false];
            }

            return ['success' => true, 'handled' => true];
        });
    }
}
