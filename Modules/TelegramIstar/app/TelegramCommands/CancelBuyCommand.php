<?php

namespace Modules\TelegramIstar\TelegramCommands;

use App\Helpers\DiscordLogHelper;
use <PERSON><PERSON>les\TelegramBot\Models\TelegramUser;
use Mo<PERSON>les\TelegramBot\TelegramCommands\CommandInterface;
use <PERSON><PERSON>les\TelegramClient\Services\TelegramBotClient;
use Mo<PERSON>les\TelegramTonPayment\Models\VerificationTransaction;

class CancelBuyCommand implements CommandInterface
{
    /**
     * Handle the cancel buy command (callback from payment confirmation)
     */
    public function handle(string|int $chatId, TelegramUser $user, ?array $params = []): array
    {
        $transactionId = $params[0] ?? null;

        if (!$transactionId) {
            DiscordLogHelper::error('Cancel buy command called without transaction ID for user ' . $user->tele_id);
            return ['success' => true, 'handled' => true];
        }

        $transaction = VerificationTransaction::query()
            ->where('id', $transactionId)
            ->where('user_id', $user->id)
            ->first();

        if (!$transaction) {
            DiscordLogHelper::error('Cancel buy command called with invalid transaction ID for user ' . $user->tele_id);
            return ['success' => true, 'handled' => true];
        }

        $transaction->status = 'cancelled';
        $transaction->save();

        return [
            'success' => true,
            'handled' => true,
        ];
    }
}
