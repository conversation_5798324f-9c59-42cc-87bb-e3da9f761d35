<?php

namespace Modules\TelegramIstar\TelegramCommands;

use App\Helpers\DiscordLogHelper;
use Modules\TelegramBot\Models\TelegramUser;
use Modules\TelegramBot\Services\MessageContextService;
use Mo<PERSON>les\TelegramBot\TelegramCommands\AdminMiddleware;
use Mo<PERSON>les\TelegramBot\TelegramCommands\CommandInterface;
use Modules\TelegramClient\Services\TelegramBotClient;
use Modules\TelegramTonPayment\Models\VerificationTransaction;

class ApproveWithdrawalCommand implements CommandInterface
{
    public function handle(string|int $chatId, TelegramUser $user, ?array $params = []): array
    {
        return AdminMiddleware::check($user, $chatId, function () use ($chatId, $user, $params) {
            $withdrawalId = $params[0] ?? null;
            if (! $withdrawalId) {
                DiscordLogHelper::error('ApproveWithDrawl: Missing withdrawal ID in parameters');

                return ['success' => false, 'handled' => false];
            }

            try {
                VerificationTransaction::where('id', $withdrawalId)
                    ->update(['status' => 'approved']);

                $botClient = new TelegramBotClient;

                DiscordLogHelper::log('Approving withdrawal ID '.$withdrawalId.' by admin '.$user->tele_id);

                $message = "✅ Withdrawal ID {$withdrawalId} has been approved.";
                $botClient->sendMessage(
                    $chatId,
                    $message,
                    [
                        'parse_mode' => 'Markdown',
                    ]
                );

                // Delete the original notification message
                MessageContextService::deleteOriginalMessage($botClient, $chatId);

            } catch (\Exception $e) {
                DiscordLogHelper::error('ApproveWithDrawl failed: '.$e->getMessage());
                DiscordLogHelper::error('Stack trace: '.$e->getTraceAsString());

                return ['success' => false, 'handled' => false];
            }

            return ['success' => true, 'handled' => true];
        });
    }
}
