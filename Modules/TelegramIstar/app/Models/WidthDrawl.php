<?php

namespace Modules\TelegramIstar\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Modules\TelegramBot\Models\TelegramUser;
// use Modules\TelegramIstar\Database\Factories\WidthDrawFactory;

/**
 * Class WidthDrawl
 *
 * @property int $id
 * @property int $user_id
 * @property string $status
 * @property float $amount
 * @property string $currency
 * @property string $address
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 */

class WidthDrawl extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'user_id',
        'status',
        'amount',
        'address',
        'type',
        'note',
    ];

    /**
     * The table associated with the model.
     */
    protected $table = 'withdrawl';

    /**
     * Get the user that owns the withdrawal.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(TelegramUser::class, 'user_id');
    }
}
