<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('telegram_user', function (Blueprint $table) {
            $table->timestamp('premium_expires_at')->nullable()->after('referral_code');
            $table->string('premium_type')->nullable()->after('premium_expires_at');
            
            // Add index for premium expiration queries
            $table->index('premium_expires_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('telegram_user', function (Blueprint $table) {
            $table->dropIndex(['premium_expires_at']);
            $table->dropColumn(['premium_expires_at', 'premium_type']);
        });
    }
};
