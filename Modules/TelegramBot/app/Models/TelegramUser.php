<?php

namespace Modules\TelegramBot\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Modules\TimeLockedWallet\Traits\UseLedgerEntry;
use Modules\TelegramIstar\Helpers\IstarHelper;
use Modules\TelegramTonPayment\Traits\UseTonPayment;

class TelegramUser extends Model
{
    use HasFactory;
    use UseLedgerEntry;
    use UseTonPayment;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'tele_id',
        'name',
        'username',
        'language_code',
        'avatar_url',
        'last_active',
        'balance_ton',
        'balance_star',
        'referral_code',
    ];

    protected $table = 'telegram_user';

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'balance_ton' => 'decimal:8',
        'balance_star' => 'decimal:8',
        'last_active' => 'datetime',
    ];

    public function isAdmin(): bool
    {
        $adminIds = env('TELEGRAM_ADMIN_IDS') ?? '';
        $adminIds = explode(',', $adminIds);

        return in_array($this->tele_id, $adminIds);
    }

    public function addBalance(float $amount, string $currency = 'TON', ?string $description = null): void
    {
        DB::transaction(function () use ($amount, $currency, $description) {
            $previousBalance = $this->getBalance($currency);

            $newBalance = $previousBalance + $amount;

            if ($newBalance < 0) {
                throw new \InvalidArgumentException("Insufficient balance. Current: {$previousBalance}, Attempting to change by: {$amount}");
            }

            $this->addLedgerEntry(
                amount: abs($amount),
                type: $amount >= 0 ? 'credit' : 'debit',
                currency: $currency,
                note: $description
            );

            $balanceField = $currency === 'TON' ? 'balance_ton' : 'balance_star';
            $this->{$balanceField} = $newBalance;
            $this->save();
        });
    }

    public function getBalance(string $currency = 'STAR'): float
    {
        $field = $currency === 'TON' ? 'balance_ton' : 'balance_star';

        return $this->{$field} ?? 0.0;
    }

    public function getLockedDay()
    {
        $vipInfo = (new IstarHelper())->getVipInfo($this);

        return match ($vipInfo['vip_level']) {
            0 => 21,
            1 => 14,
            2 => 7,
            3, 4 => 3,
            5 => 1,
            default => 21,
        };
    }
}
