<?php

namespace Modules\TelegramBot\TelegramCommands;

use Mo<PERSON>les\TelegramBot\Models\TelegramUser;
use Mo<PERSON>les\TelegramClient\Services\TelegramBotClient;

class AdminMiddleware
{
    public static function check(TelegramUser $user, string $chatId, callable $next): array
    {
        if (! $user->isAdmin()) {
            $botClient = new TelegramBotClient;
            $botClient->sendMessage($chatId, 'You do not have permission to use this command.');

            return ['success' => false, 'handled' => false];
        }

        return $next();
    }
}
