<?php

namespace Modules\TelegramBot\Components;
use Modules\TelegramBot\Interfaces\ButtonInterface;
use Modules\TelegramBot\Models\TelegramUser;

class BackToLastCommand implements ButtonInterface
{
    public static function make(?array $payload = []): array
    {
        $user = $payload['user'] ?? null;
        if (! $user instanceof TelegramUser) {
            throw new \InvalidArgumentException('Payload must contain a valid TelegramUser instance under the "user" key.');
        }

        return [
            'text' => '🔙 Back',
            'callback_data' => $user->last_command ?? 'start',
        ];
    }
}
