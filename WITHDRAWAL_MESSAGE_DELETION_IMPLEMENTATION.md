# Withdrawal Message Deletion Implementation

## Overview

This implementation adds automatic deletion of withdrawal notification messages after admin approval or rejection actions are completed. The solution ensures that once an admin processes a withdrawal request, the original notification message with buttons is removed from the chat to keep it clean and prevent confusion.

## Questions Answered

### 1. Does the Telegram Bot API support deleting/clearing messages?
**Yes**, the Telegram Bot API provides the `deleteMessage` method that allows bots to delete messages. The `TelegramBotClient` class already has this functionality implemented in the `deleteMessage(chatId, messageId)` method.

### 2. How can we modify the existing code to automatically delete notification messages?
The implementation uses a **message context service** approach that:
- Stores the original message context when processing callback queries
- Allows withdrawal commands to access the message ID for deletion
- Maintains backward compatibility with existing command interfaces

### 3. What's the best approach for message cleanup functionality?
The implemented solution uses a **service-based approach** with the following benefits:
- **Non-invasive**: Doesn't require changing existing command interfaces
- **Reusable**: Can be used by other commands that need message context
- **Clean**: Automatically manages context lifecycle
- **Safe**: Includes proper error handling and logging

## Implementation Details

### New Components

#### 1. MessageContextService
**File**: `Modules/TelegramBot/app/Services/MessageContextService.php`

A service class that stores and manages message context during callback query processing:

```php
class MessageContextService
{
    // Stores current message context
    public static function setMessageContext(?array $messageContext): void
    
    // Retrieves message context
    public static function getMessageContext(): ?array
    
    // Gets message ID for deletion
    public static function getMessageId(): ?int
    
    // Gets chat ID from context
    public static function getChatId(): int|string|null
    
    // Clears context after processing
    public static function clearMessageContext(): void
}
```

#### 2. Updated QueryCallbackDispatcher
**File**: `Modules/TelegramBot/app/Services/QueryCallbackDispatcher.php`

Enhanced to store message context before command execution:

```php
// Store message context for commands that need to delete/modify the original message
MessageContextService::setMessageContext($message);

$result = $handler->handle($message['chat']['id'], $user, $data['params'] ?? []);

// Clear message context after handling
MessageContextService::clearMessageContext();
```

#### 3. Enhanced Withdrawal Commands
**Files**: 
- `Modules/TelegramIstar/app/TelegramCommands/ApproveWithdrawalCommand.php`
- `Modules/TelegramIstar/app/TelegramCommands/RejectWithdrawalCommand.php`

Both commands now include:
- Message deletion functionality after successful approval/rejection
- Comprehensive error handling and logging
- Graceful fallback if message deletion fails

```php
// Delete the original notification message
$this->deleteOriginalMessage($botClient, $chatId);

private function deleteOriginalMessage(TelegramBotClient $botClient, string|int $chatId): void
{
    try {
        $messageId = MessageContextService::getMessageId();
        
        if ($messageId) {
            $deleteResult = $botClient->deleteMessage($chatId, $messageId);
            // Handle success/failure with appropriate logging
        }
    } catch (\Exception $e) {
        // Log errors without breaking the main workflow
    }
}
```

## Workflow

### Before Implementation
1. Admin receives withdrawal notification with approve/reject buttons
2. Admin clicks approve/reject
3. System updates withdrawal status
4. System sends confirmation message
5. **Original notification remains in chat** ❌

### After Implementation
1. Admin receives withdrawal notification with approve/reject buttons
2. Admin clicks approve/reject
3. System stores message context
4. System updates withdrawal status
5. System sends confirmation message
6. **System deletes original notification message** ✅
7. System clears message context

## Error Handling

The implementation includes comprehensive error handling:

- **Graceful degradation**: If message deletion fails, the main approval/rejection still succeeds
- **Detailed logging**: All operations are logged for debugging
- **Context cleanup**: Message context is always cleared, even on errors
- **Safe operations**: No exceptions bubble up to break the main workflow

## Benefits

1. **Clean Chat Interface**: Removes processed notifications automatically
2. **Prevents Confusion**: Admins can't accidentally process the same withdrawal twice
3. **Better UX**: Cleaner admin chat experience
4. **Maintainable**: Clean, service-based architecture
5. **Extensible**: Other commands can easily use the same message context system

## Testing

To test the implementation:

1. Create a withdrawal request that triggers admin notification
2. Click approve or reject button
3. Verify that:
   - Withdrawal status is updated correctly
   - Confirmation message is sent
   - Original notification message is deleted
   - Appropriate logs are generated

## Backward Compatibility

The implementation maintains full backward compatibility:
- Existing command interfaces remain unchanged
- Other commands continue to work normally
- No breaking changes to the existing codebase
- Optional functionality that doesn't affect other systems

## Future Enhancements

This foundation enables future enhancements such as:
- Message editing instead of deletion
- Batch message operations
- Message context for other command types
- Enhanced admin notification features
