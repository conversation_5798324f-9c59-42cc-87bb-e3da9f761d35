layout like below code
```py
import math
from telebot import types
from database import get_rate
from utils import day_number_to_text

PREMIUM_PACKAGES = {
    90: 14,
    180: 19,
    365: 33
}

def register_show_buy_premium_with_ton_function(bot):
    @bot.callback_query_handler(func=lambda call: call.data == "show_buy_premium_with_ton")
    def _(call):
        ton_rate = get_rate('TON/USDT')
        if not ton_rate:
            bot.answer_callback_query(call.id, "TON rate not available. Please try again later.")
            return

        description = (
            "🔷 Buy Telegram Premium with TON 🔷\n\n"
            "Live TON price: " + str(ton_rate) + " USD\n\n"
            "Select a premium package:\n\n"
        )

        keyboard = types.InlineKeyboardMarkup(row_width=2)
        buttons = []

        for days, usd in PREMIUM_PACKAGES.items():
            ton_price = math.ceil((usd / ton_rate) * 100) / 100
            period = day_number_to_text(days)

            buttons.append(
                types.InlineKeyboardButton(
                    f"{period} (${usd})",
                    callback_data=f"buy_premium_with_ton_{days}"
                )
            )
            description += f"• ${usd} (≈{ton_price} TON)\n"

        for i in range(0, len(buttons), 2):
            if i + 1 < len(buttons):
                keyboard.add(buttons[i], buttons[i + 1])
            else:
                keyboard.add(buttons[i])

        keyboard.add(
            types.InlineKeyboardButton("🔙 Back", callback_data="show_buy_premium_menu")
        )

        bot.edit_message_text(
            chat_id=call.message.chat.id,
            message_id=call.message.message_id,
            text=description,
            reply_markup=keyboard
        )

```
logic like BuyStar flow already implement in this project.

- Press Buy Premium -> show selection for self / other -> show layout -> purchase logic (like BuyStar) -> list like case buy star for other
